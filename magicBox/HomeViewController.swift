//
//  HomeViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON>an on 2025/5/18.
//

import UIKit

class HomeViewController: UIViewController {
    
    // MARK: - Properties
    private var tableView: UITableView!
    
    // 定义菜单项数据
    private let menuItems = [
        (NSLocalizedString("home.menu.database_example", comment: "Database Example"), "database", ColorsViewController.self)
    ]

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        
        // 数据库使用示例
        loadDatabaseData()
        
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        navigationController?.navigationBar.prefersLargeTitles = true
        title = NSLocalizedString("home.title", comment: "Home")
        
        setupTableView()
    }
    
    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "MenuCell")
        tableView.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(tableView)
        
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    // MARK: - 数据库操作示例
    private func loadDatabaseData() {
        Logger.database.enter()
        Logger.database.info("正在加载数据库数据...")
        
        do {
            let dbManager = DatabaseManager.shared
            
            // 获取所有模板
            let templates = try dbManager.getAllTemplates()
            Logger.database.info("📋 当前有 \(templates.count) 个模板")
            
            // 获取所有物品
            let items = try dbManager.getAllItems()
            Logger.database.info("📦 当前有 \(items.count) 个物品")
            
            
        } catch {
            Logger.database.error("❌ 加载数据库数据失败: \(error.localizedDescription)")
            Logger.database.object(error, name: "数据库加载错误")
        }
        
        Logger.database.exit()
    }

    
    
    
}

// MARK: - UITableViewDataSource
extension HomeViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return menuItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MenuCell", for: indexPath)
        let menuItem = menuItems[indexPath.row]
        
        cell.textLabel?.text = menuItem.0
        cell.imageView?.image = UIImage(systemName: menuItem.1)
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension HomeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let menuItem = menuItems[indexPath.row]
        let viewControllerClass = menuItem.2
        let viewController = viewControllerClass.init()
        
        // 使用自定义的TabBarAwareNavigationController，会自动处理tabbar的显示隐藏
        navigationController?.pushViewController(viewController, animated: true)
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return NSLocalizedString("home.menu.title", comment: "Feature Menu")
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56
    }
} 

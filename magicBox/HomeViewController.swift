//
//  HomeViewController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/18.
//

import UIKit
import SnapKit
import YPImagePicker
import PermissionsKit
import PhotoLibraryPermission

class HomeViewController: BaseViewController {
    
    // MARK: - Properties
    private var tableView: UITableView!
    
    // 定义菜单项数据
    private let menuItems = [
        (NSLocalizedString("home.menu.database_example", comment: "Database Example"), "database", OptionsViewController.self)
    ]

    // MARK: - BaseViewController Override
    override func handleFloatingButtonTap() {
        // 创建警告对话框
        let alert = UIAlertController(
            title: NSLocalizedString("home.add.title", comment: "Add New Item"),
            message: NSLocalizedString("home.add.message", comment: "Choose an option to add"),
            preferredStyle: .actionSheet
        )
        
        // 添加选项
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.photo", comment: "Add Photo"),
            style: .default,
            handler: { [weak self] _ in
                self?.showImagePicker()
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.template", comment: "Add Template"),
            style: .default,
            handler: { [weak self] _ in
                // TODO: 处理添加模板的逻辑
                self?.showAlert(title: "提示", message: "添加模板功能即将推出")
            }
        ))
        
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("home.add.item", comment: "Add Item"),
            style: .default,
            handler: { [weak self] _ in
                // TODO: 处理添加项目的逻辑
                self?.showAlert(title: "提示", message: "添加项目功能即将推出")
            }
        ))
        
        // 添加取消按钮
        alert.addAction(UIAlertAction(
            title: NSLocalizedString("common.cancel", comment: "Cancel"),
            style: .cancel
        ))
        
        // 在 iPad 上需要设置弹出位置
        if let popoverController = alert.popoverPresentationController {
            popoverController.sourceView = view
            popoverController.sourceRect = CGRect(
                x: view.bounds.width - 20,
                y: view.bounds.height - 20,
                width: 1,
                height: 1
            )
        }
        
        // 显示对话框
        present(alert, animated: true)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        
    }
    
    private func setupUI() {
        title = NSLocalizedString("home.title", comment: "Home")
        showsFloatingButton = true
        setupTableView()
    }
    
    private func setupTableView() {
        tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "MenuCell")
        
        view.addSubview(tableView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
    
    private func showImagePicker() {
        // 先检查相册权限
        PermissionsKit.Permission.request { [weak self] status in
            switch status {
            case .authorized:
                // 用户授权，显示图片选择器
                self?.presentImagePicker()
            case .denied:
                // 用户拒绝，显示提示
                self?.showAlert(
                    title: NSLocalizedString("permission.denied.title", comment: "Permission Denied"),
                    message: NSLocalizedString("permission.photos.denied.message", comment: "Please enable photos access in Settings")
                )
            case .notDetermined:
                // 理论上不会到这里，因为request会处理这种情况
                break
            }
        }
    }
    
    private func presentImagePicker() {
        var config = YPImagePickerConfiguration()
        // 配置 YPImagePicker
        config.library.maxNumberOfItems = 1     // 最多选择1张图片
        config.library.mediaType = .photo       // 只显示照片
        config.screens = [.library]             // 只显示相册
        config.library.defaultMultipleSelection = false  // 默认单选
        config.library.skipSelectionsGallery = true     // 选择后直接返回
        
        let picker = YPImagePicker(configuration: config)
        picker.didFinishPicking { [weak self] items, cancelled in
            if cancelled {
                picker.dismiss(animated: true)
                return
            }
            
            if let photo = items.singlePhoto {
                // TODO: 处理选中的图片
                print("Selected photo: \(photo.image)")
                // 这里可以添加处理图片的代码
            }
            
            picker.dismiss(animated: true)
        }
        
        present(picker, animated: true)
    }
    
}

// MARK: - UITableViewDataSource
extension HomeViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return menuItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "MenuCell", for: indexPath)
        let menuItem = menuItems[indexPath.row]
        
        cell.textLabel?.text = menuItem.0
        cell.imageView?.image = UIImage(systemName: menuItem.1)
        cell.accessoryType = .disclosureIndicator
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension HomeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let menuItem = menuItems[indexPath.row]
        let viewControllerClass = menuItem.2
        let viewController = viewControllerClass.init()
        
        // 使用自定义的TabBarAwareNavigationController，会自动处理tabbar的显示隐藏
        navigationController?.pushViewController(viewController, animated: true)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56
    }
} 

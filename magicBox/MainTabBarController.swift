//
//  MainTabBarController.swift
//  magicBox
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/18.
//

import UIKit

class MainTabBarController: UITabBarController {

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
    }
    
    private func setupTabBar() {
        // 创建各个页面的导航控制器，使用自定义的TabBarAwareNavigationController
        let homeVC = HomeViewController()
        let homeNavController = TabBarAwareNavigationController(rootViewController: homeVC)
        homeNavController.tabBarItem = UITabBarItem(title: NSLocalizedString("tab.home", comment: "Home"), image: UIImage(systemName: "house"), tag: 0)
        
        let calendarVC = CalendarViewController()
        let calendarNavController = TabBarAwareNavigationController(rootViewController: calendarVC)
        calendarNavController.tabBarItem = UITabBarItem(title: NSLocalizedString("tab.calendar", comment: "Calendar"), image: UIImage(systemName: "calendar"), tag: 1)
        
        let statisticsVC = StatisticsViewController()
        let statisticsNavController = TabBarAwareNavigationController(rootViewController: statisticsVC)
        statisticsNavController.tabBarItem = UITabBarItem(title: NSLocalizedString("tab.statistics", comment: "Statistics"), image: UIImage(systemName: "chart.bar.fill"), tag: 2)
        
        let settingsVC = SettingsViewController()
        let settingsNavController = TabBarAwareNavigationController(rootViewController: settingsVC)
        settingsNavController.tabBarItem = UITabBarItem(title: NSLocalizedString("tab.settings", comment: "Settings"), image: UIImage(systemName: "gearshape.fill"), tag: 3)
        
        // 设置tab bar的视图控制器
        viewControllers = [homeNavController, calendarNavController, statisticsNavController, settingsNavController]
        
        // 自定义tab bar外观
        tabBar.tintColor = .systemBlue
        tabBar.backgroundColor = .systemBackground
        
        // iOS 15+ 的透明度设置
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = .systemBackground
            tabBar.standardAppearance = appearance
            tabBar.scrollEdgeAppearance = appearance
        }
    }
} 
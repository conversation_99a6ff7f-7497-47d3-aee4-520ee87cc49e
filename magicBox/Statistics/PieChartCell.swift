import UIKit
import SwiftUI
import Charts
import SnapKit

class PieChartCell: UITableViewCell, StatisticsCellHeightCalculatable {
    static let reuseIdentifier = "CategoryChartCell"
    
    private var hostingController: UIHostingController<PieChartView>?
    private let containerView = UIView()
    private let headerView = StatisticsHeaderView()
    private var heightConstraint: Constraint? // 添加约束引用
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupHostingController()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        selectionStyle = .none
        
        containerView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        containerView.layer.cornerRadius = 12
        containerView.clipsToBounds = true
        
        contentView.addSubview(containerView)
        containerView.addSubview(headerView)
        
        // 修改容器视图的约束
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(
                top: 0,
                left: StatisticsConstants.Layout.contentInset,
                bottom: StatisticsConstants.Layout.contentInset,
                right: StatisticsConstants.Layout.contentInset
            ))
        }
        
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(StatisticsConstants.Layout.headerHeight)
        }
    }
    
    private func setupHostingController() {
        // 初始化时创建 UIHostingController，使用空数据
        let hostingController = UIHostingController<PieChartView>(rootView: PieChartView(data: []))
        hostingController.view.backgroundColor = .clear
        self.hostingController = hostingController
        
        if let hostingView = hostingController.view {
            containerView.addSubview(hostingView)
            hostingView.snp.makeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.leading.trailing.equalToSuperview()
                self.heightConstraint = make.height.equalTo(0).constraint // 保存高度约束引用
            }
        }
    }
    
    func configure(with data: [CategoryData]) {
        // 配置标题
        headerView.configure(with: NSLocalizedString("statistics.category_distribution", comment: "Category distribution chart title"))
        
        // 更新现有 hostingController 的根视图
        hostingController?.rootView = PieChartView(data: data)
        
        // 更新高度约束
        let containerWidth = UIScreen.main.bounds.width - 
            2 * StatisticsConstants.Layout.contentInset - 
            2 * StatisticsConstants.Layout.chartSidePadding
        let pieChartView = PieChartView(data: data)
        let legendHeight = pieChartView.calculateLegendHeight(containerWidth: containerWidth)
        let totalHeight = StatisticsConstants.Layout.pieChartContentHeight + legendHeight
        
        // 使用保存的约束引用更新高度
        heightConstraint?.update(offset: totalHeight)
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        // 重置为空数据
        hostingController?.rootView = PieChartView(data: [])
        // 重置高度
        heightConstraint?.update(offset: 0)
    }
    
    static func calculateHeight(with data: Any) -> CGFloat {
        guard let categoryData = data as? [CategoryData] else { return 0 }
        
        // 计算总高度：header高度 + 内容边距 + 饼图高度 + 图例高度
        let containerWidth = UIScreen.main.bounds.width - 
            2 * StatisticsConstants.Layout.contentInset - 
            2 * StatisticsConstants.Layout.chartSidePadding
        
        let pieChartView = PieChartView(data: categoryData)
        let legendHeight = pieChartView.calculateLegendHeight(containerWidth: containerWidth)
        
        return StatisticsConstants.Layout.headerHeight + 
               StatisticsConstants.Layout.contentInset +
               StatisticsConstants.Layout.pieChartContentHeight + 
               legendHeight
    }
}

// MARK: - 饼图视图
struct PieChartView: View {
    let data: [CategoryData]
    
    private var total: Int {
        data.map(\.count).reduce(0, +)
    }
    
    // 将方法改为 internal 访问级别
    func calculateLegendHeight(containerWidth: CGFloat) -> CGFloat {
        let spacing = StatisticsConstants.Layout.legendItemSpacing
        let itemHeight = StatisticsConstants.Layout.legendItemHeight
        let itemPadding = StatisticsConstants.Layout.legendItemPadding
        
        var currentLineWidth: CGFloat = 0
        var lineCount: Int = 1
        
        for item in data {
            // 计算完整文本的宽度
            let text = "\(item.category) \(item.count)(\(String(format: "%.1f%%", Double(item.count) / Double(total) * 100)))"
            let itemWidth = (text as NSString).size(withAttributes: [.font: UIFont.systemFont(ofSize: 12)]).width + 
                StatisticsConstants.Layout.legendDotSize + 
                spacing + 
                itemPadding
            
            if currentLineWidth + itemWidth > containerWidth {
                lineCount += 1
                currentLineWidth = itemWidth
            } else {
                currentLineWidth += itemWidth + spacing
            }
        }
        
        return CGFloat(lineCount) * itemHeight + 
            CGFloat(lineCount - 1) * spacing + 
            StatisticsConstants.Layout.legendVerticalPadding
    }
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 饼图部分
                ZStack {
                    Chart(data) { item in
                        SectorMark(
                            angle: .value("数量", item.count),
                            innerRadius: .ratio(StatisticsConstants.Layout.pieChartInnerCircleRatio),
                            angularInset: 1.5
                        )
                        .cornerRadius(4)
                        .foregroundStyle(item.color)
                        .annotation(position: .overlay) {
                            Text("\(item.category) \(item.count)(\(String(format: "%.1f%%", Double(item.count) / Double(total) * 100)))")
                                .font(.caption2)
                                .bold()
                                .foregroundColor(Color(.darkGray))
                                .padding(2)
                                .opacity(Double(item.count) / Double(total) > 0.05 ? 1 : 0)
                        }
                    }
                    
                    // 中心信息
                    VStack(spacing: 4) {
                        Text("分类数据")
                            .font(.system(size: 15, weight: .medium))
                            .foregroundColor(.primary)
                        Text("共\(total)个")
                            .font(.system(size: 13))
                            .foregroundColor(.secondary)
                    }
                }
                .frame(height: StatisticsConstants.Layout.pieChartContentHeight)
                .padding(.horizontal, StatisticsConstants.Layout.chartSidePadding)
                .chartLegend(.hidden)
                
                // 图例部分
                FlowLayout(spacing: StatisticsConstants.Layout.legendItemSpacing) {
                    ForEach(data, id: \.category) { item in
                        HStack(spacing: 4) {
                            Circle()
                                .fill(item.color)
                                .frame(width: StatisticsConstants.Layout.legendDotSize, 
                                       height: StatisticsConstants.Layout.legendDotSize)
                            Text("\(item.category) \(item.count)(\(String(format: "%.1f%%", Double(item.count) / Double(total) * 100)))")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                    }
                }
                .padding(.horizontal, StatisticsConstants.Layout.chartSidePadding)
                .padding(.vertical, StatisticsConstants.Layout.legendVerticalPadding / 2)
                .frame(height: calculateLegendHeight(containerWidth: geometry.size.width - 2 * StatisticsConstants.Layout.chartSidePadding))
            }
            .frame(width: geometry.size.width)
            .background(Color(uiColor: .clear))
        }
    }
} 

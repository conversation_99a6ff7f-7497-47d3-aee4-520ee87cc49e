import UIKit
import SwiftUI
import Charts
import SnapKit

class LineChartCell: UITableViewCell, StatisticsCellHeightCalculatable {
    static let reuseIdentifier = "LineChartCell"
    
    private var hostingController: UIHostingController<LineChartView>?
    private let containerView = UIView()
    private let headerView = StatisticsHeaderView()
    private var heightConstraint: Constraint? // 添加约束引用
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupHostingController()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        selectionStyle = .none
        
        containerView.backgroundColor = AppTheme.Colors.secondaryGroupedBackground
        containerView.layer.cornerRadius = 12
        containerView.clipsToBounds = true
        
        contentView.addSubview(containerView)
        containerView.addSubview(headerView)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(
                top: 0,
                left: StatisticsConstants.Layout.contentInset,
                bottom: StatisticsConstants.Layout.contentInset,
                right: StatisticsConstants.Layout.contentInset
            ))
        }
        
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(StatisticsConstants.Layout.headerHeight)
        }
    }
    
    private func setupHostingController() {
        // 初始化时创建 UIHostingController，使用空数据
        let hostingController = UIHostingController<LineChartView>(rootView: LineChartView(data: []))
        hostingController.view.backgroundColor = .clear
        self.hostingController = hostingController
        
        if let hostingView = hostingController.view {
            containerView.addSubview(hostingView)
            hostingView.snp.makeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.leading.trailing.equalToSuperview()
                self.heightConstraint = make.height.equalTo(0).constraint // 保存高度约束引用
            }
        }
    }
    
    func configure(with data: [MonthlyData]) {
        // 配置标题
        headerView.configure(with: NSLocalizedString("statistics.monthly_trend", comment: "Monthly trend chart title"))
        
        // 更新现有 hostingController 的根视图
        hostingController?.rootView = LineChartView(data: data)
        
        // 更新高度约束
        heightConstraint?.update(offset: StatisticsConstants.Layout.lineChartHeight)
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        // 重置为空数据
        hostingController?.rootView = LineChartView(data: [])
        // 重置高度
        heightConstraint?.update(offset: 0)
    }
    
    static func calculateHeight(with data: Any) -> CGFloat {
        // 折线图高度是固定的
        return StatisticsConstants.Layout.lineChartHeight + 
               StatisticsConstants.Layout.headerHeight + 
               StatisticsConstants.Layout.contentInset
    }
}

// MARK: - 月度趋势图表
private struct LineChartView: View {
    let data: [MonthlyData]
    
    var body: some View {
        GeometryReader { geometry in
            Chart(data) { item in
                LineMark(
                    x: .value("月份", item.month),
                    y: .value("数量", item.count)
                )
                .foregroundStyle(Color(uiColor: AppTheme.Colors.primary))
                .interpolationMethod(.catmullRom)
                
                PointMark(
                    x: .value("月份", item.month),
                    y: .value("数量", item.count)
                )
                .foregroundStyle(Color(uiColor: AppTheme.Colors.primary))
                .annotation(position: .top) {
                    Text("\(item.count)")
                        .font(.caption2)
                        .bold()
                        .foregroundColor(Color(.darkGray))
                        .padding(.vertical, 4)
                }
            }
            .chartYAxis {
                AxisMarks(position: .leading)
            }
            .frame(
                width: geometry.size.width - StatisticsConstants.Layout.chartSidePadding * 2,
                height: StatisticsConstants.Layout.lineChartContentHeight
            )
            .padding(.horizontal, StatisticsConstants.Layout.chartSidePadding)
            .padding(.top, StatisticsConstants.Layout.chartTopPadding)
            .padding(.bottom, StatisticsConstants.Layout.chartBottomPadding)
            .background(Color(uiColor: .clear))
        }
    }
} 

// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		B60BF0762E1FB6FA00DF718C /* FloatingPanel in Frameworks */ = {isa = PBXBuildFile; productRef = B60BF0752E1FB6FA00DF718C /* FloatingPanel */; };
		B61632EC2E1D22450058DB2A /* SnapKit in Frameworks */ = {isa = PBXBuildFile; productRef = B61632EB2E1D22450058DB2A /* SnapKit */; };
		B641B1672E24DD7D0071AF7E /* YPImagePicker in Frameworks */ = {isa = PBXBuildFile; productRef = B641B1662E24DD7D0071AF7E /* YPImagePicker */; };
		B6A44A652E1BD26700E8C310 /* ZipArchive in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A642E1BD26700E8C310 /* ZipArchive */; };
		B6A44A682E1BD2EE00E8C310 /* SQLite in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A672E1BD2EE00E8C310 /* SQLite */; };
		B6A44A6B2E1BE78E00E8C310 /* Tiercel in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A6A2E1BE78E00E8C310 /* Tiercel */; };
		B6A44A6E2E1BE7F400E8C310 /* SKPhotoBrowser in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A6D2E1BE7F400E8C310 /* SKPhotoBrowser */; };
		B6A44A712E1BE88000E8C310 /* StoreHelper in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A702E1BE88000E8C310 /* StoreHelper */; };
		B6A44A742E1BE89B00E8C310 /* Files in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A732E1BE89B00E8C310 /* Files */; };
		B6A44A772E1BE8D000E8C310 /* Device in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A762E1BE8D000E8C310 /* Device */; };
		B6A44A7A2E1BE9E800E8C310 /* CameraPermission in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A792E1BE9E800E8C310 /* CameraPermission */; };
		B6A44A7C2E1BE9E800E8C310 /* FaceIDPermission in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A7B2E1BE9E800E8C310 /* FaceIDPermission */; };
		B6A44A7E2E1BE9E800E8C310 /* NotificationPermission in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A7D2E1BE9E800E8C310 /* NotificationPermission */; };
		B6A44A802E1BE9E800E8C310 /* PhotoLibraryPermission in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A7F2E1BE9E800E8C310 /* PhotoLibraryPermission */; };
		B6A44A832E1BEA1F00E8C310 /* SFSafeSymbols in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A822E1BEA1F00E8C310 /* SFSafeSymbols */; };
		B6A44A862E1BECD000E8C310 /* HXPhotoPicker in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A852E1BECD000E8C310 /* HXPhotoPicker */; };
		B6A44A892E1BECF600E8C310 /* Popovers in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A882E1BECF600E8C310 /* Popovers */; };
		B6A44A8C2E1BF14800E8C310 /* HorizonCalendar in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A8B2E1BF14800E8C310 /* HorizonCalendar */; };
		B6A44A8F2E1BF3B000E8C310 /* CountryPicker in Frameworks */ = {isa = PBXBuildFile; productRef = B6A44A8E2E1BF3B000E8C310 /* CountryPicker */; };
		B6AAB6042E210D6900540D38 /* Mantis in Frameworks */ = {isa = PBXBuildFile; productRef = B6AAB6032E210D6900540D38 /* Mantis */; };
		F14D31FE2DEBEB1900F80C56 /* IQKeyboardManagerSwift in Frameworks */ = {isa = PBXBuildFile; productRef = F14D31FD2DEBEB1900F80C56 /* IQKeyboardManagerSwift */; };
		F14D32012DEBEBE200F80C56 /* LookinServer in Frameworks */ = {isa = PBXBuildFile; productRef = F14D32002DEBEBE200F80C56 /* LookinServer */; };
		F1D1365C2DD9EC46005B1D4C /* OpenCV in Frameworks */ = {isa = PBXBuildFile; productRef = F1D1365B2DD9EC46005B1D4C /* OpenCV */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		F1D136422DD9EA4C005B1D4C /* magicBox.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = magicBox.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		F1D136542DD9EA4E005B1D4C /* Exceptions for "magicBox" folder in "magicBox" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = F1D136412DD9EA4C005B1D4C /* magicBox */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F1D136442DD9EA4C005B1D4C /* magicBox */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				F1D136542DD9EA4E005B1D4C /* Exceptions for "magicBox" folder in "magicBox" target */,
			);
			path = magicBox;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F1D1363F2DD9EA4C005B1D4C /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B6A44A832E1BEA1F00E8C310 /* SFSafeSymbols in Frameworks */,
				B6A44A772E1BE8D000E8C310 /* Device in Frameworks */,
				B6A44A7C2E1BE9E800E8C310 /* FaceIDPermission in Frameworks */,
				B6A44A742E1BE89B00E8C310 /* Files in Frameworks */,
				B6A44A8C2E1BF14800E8C310 /* HorizonCalendar in Frameworks */,
				B6A44A712E1BE88000E8C310 /* StoreHelper in Frameworks */,
				B6A44A862E1BECD000E8C310 /* HXPhotoPicker in Frameworks */,
				B6A44A802E1BE9E800E8C310 /* PhotoLibraryPermission in Frameworks */,
				B6A44A6E2E1BE7F400E8C310 /* SKPhotoBrowser in Frameworks */,
				B61632EC2E1D22450058DB2A /* SnapKit in Frameworks */,
				B6AAB6042E210D6900540D38 /* Mantis in Frameworks */,
				B6A44A682E1BD2EE00E8C310 /* SQLite in Frameworks */,
				B641B1672E24DD7D0071AF7E /* YPImagePicker in Frameworks */,
				B6A44A7E2E1BE9E800E8C310 /* NotificationPermission in Frameworks */,
				B60BF0762E1FB6FA00DF718C /* FloatingPanel in Frameworks */,
				F1D1365C2DD9EC46005B1D4C /* OpenCV in Frameworks */,
				B6A44A7A2E1BE9E800E8C310 /* CameraPermission in Frameworks */,
				F14D32012DEBEBE200F80C56 /* LookinServer in Frameworks */,
				B6A44A892E1BECF600E8C310 /* Popovers in Frameworks */,
				B6A44A8F2E1BF3B000E8C310 /* CountryPicker in Frameworks */,
				B6A44A6B2E1BE78E00E8C310 /* Tiercel in Frameworks */,
				F14D31FE2DEBEB1900F80C56 /* IQKeyboardManagerSwift in Frameworks */,
				B6A44A652E1BD26700E8C310 /* ZipArchive in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F1D136392DD9EA4C005B1D4C = {
			isa = PBXGroup;
			children = (
				F1D136442DD9EA4C005B1D4C /* magicBox */,
				F1D136432DD9EA4C005B1D4C /* Products */,
			);
			sourceTree = "<group>";
		};
		F1D136432DD9EA4C005B1D4C /* Products */ = {
			isa = PBXGroup;
			children = (
				F1D136422DD9EA4C005B1D4C /* magicBox.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F1D136412DD9EA4C005B1D4C /* magicBox */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F1D136552DD9EA4E005B1D4C /* Build configuration list for PBXNativeTarget "magicBox" */;
			buildPhases = (
				F1D1363E2DD9EA4C005B1D4C /* Sources */,
				F1D1363F2DD9EA4C005B1D4C /* Frameworks */,
				F1D136402DD9EA4C005B1D4C /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F1D136442DD9EA4C005B1D4C /* magicBox */,
			);
			name = magicBox;
			packageProductDependencies = (
				F1D1365B2DD9EC46005B1D4C /* OpenCV */,
				F14D31FD2DEBEB1900F80C56 /* IQKeyboardManagerSwift */,
				F14D32002DEBEBE200F80C56 /* LookinServer */,
				B6A44A642E1BD26700E8C310 /* ZipArchive */,
				B6A44A672E1BD2EE00E8C310 /* SQLite */,
				B6A44A6A2E1BE78E00E8C310 /* Tiercel */,
				B6A44A6D2E1BE7F400E8C310 /* SKPhotoBrowser */,
				B6A44A702E1BE88000E8C310 /* StoreHelper */,
				B6A44A732E1BE89B00E8C310 /* Files */,
				B6A44A762E1BE8D000E8C310 /* Device */,
				B6A44A792E1BE9E800E8C310 /* CameraPermission */,
				B6A44A7B2E1BE9E800E8C310 /* FaceIDPermission */,
				B6A44A7D2E1BE9E800E8C310 /* NotificationPermission */,
				B6A44A7F2E1BE9E800E8C310 /* PhotoLibraryPermission */,
				B6A44A822E1BEA1F00E8C310 /* SFSafeSymbols */,
				B6A44A852E1BECD000E8C310 /* HXPhotoPicker */,
				B6A44A882E1BECF600E8C310 /* Popovers */,
				B6A44A8B2E1BF14800E8C310 /* HorizonCalendar */,
				B6A44A8E2E1BF3B000E8C310 /* CountryPicker */,
				B61632EB2E1D22450058DB2A /* SnapKit */,
				B60BF0752E1FB6FA00DF718C /* FloatingPanel */,
				B6AAB6032E210D6900540D38 /* Mantis */,
				B641B1662E24DD7D0071AF7E /* YPImagePicker */,
			);
			productName = magicBox;
			productReference = F1D136422DD9EA4C005B1D4C /* magicBox.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F1D1363A2DD9EA4C005B1D4C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					F1D136412DD9EA4C005B1D4C = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = F1D1363D2DD9EA4C005B1D4C /* Build configuration list for PBXProject "magicBox" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = F1D136392DD9EA4C005B1D4C;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				F1D1365A2DD9EC46005B1D4C /* XCRemoteSwiftPackageReference "opencv-spm" */,
				F14D31FC2DEBEB1900F80C56 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */,
				F14D31FF2DEBEBE200F80C56 /* XCRemoteSwiftPackageReference "LookinServer" */,
				B6A44A632E1BD26700E8C310 /* XCRemoteSwiftPackageReference "ZipArchive" */,
				B6A44A662E1BD2EE00E8C310 /* XCRemoteSwiftPackageReference "SQLite.swift" */,
				B6A44A692E1BE78E00E8C310 /* XCRemoteSwiftPackageReference "Tiercel" */,
				B6A44A6C2E1BE7F400E8C310 /* XCRemoteSwiftPackageReference "SKPhotoBrowser" */,
				B6A44A6F2E1BE88000E8C310 /* XCRemoteSwiftPackageReference "StoreHelper" */,
				B6A44A722E1BE89B00E8C310 /* XCRemoteSwiftPackageReference "Files" */,
				B6A44A752E1BE8D000E8C310 /* XCRemoteSwiftPackageReference "Device" */,
				B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */,
				B6A44A812E1BEA1F00E8C310 /* XCRemoteSwiftPackageReference "SFSafeSymbols" */,
				B6A44A842E1BECD000E8C310 /* XCRemoteSwiftPackageReference "HXPhotoPicker" */,
				B6A44A872E1BECF600E8C310 /* XCRemoteSwiftPackageReference "Popovers" */,
				B6A44A8A2E1BF14800E8C310 /* XCRemoteSwiftPackageReference "HorizonCalendar" */,
				B6A44A8D2E1BF3B000E8C310 /* XCRemoteSwiftPackageReference "CountryPicker" */,
				B61632EA2E1D22450058DB2A /* XCRemoteSwiftPackageReference "SnapKit" */,
				B60BF0742E1FB6FA00DF718C /* XCRemoteSwiftPackageReference "FloatingPanel" */,
				B6AAB6022E210D6900540D38 /* XCRemoteSwiftPackageReference "Mantis" */,
				B641B1652E24DD7D0071AF7E /* XCRemoteSwiftPackageReference "YPImagePicker" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = F1D136432DD9EA4C005B1D4C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F1D136412DD9EA4C005B1D4C /* magicBox */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F1D136402DD9EA4C005B1D4C /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F1D1363E2DD9EA4C005B1D4C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		F1D136562DD9EA4E005B1D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DC4TSZ79S4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = magicBox/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.liang.magicBox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F1D136572DD9EA4E005B1D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DC4TSZ79S4;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = magicBox/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.liang.magicBox;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F1D136582DD9EA4E005B1D4C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PL6LRUL9HV;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F1D136592DD9EA4E005B1D4C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PL6LRUL9HV;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F1D1363D2DD9EA4C005B1D4C /* Build configuration list for PBXProject "magicBox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F1D136582DD9EA4E005B1D4C /* Debug */,
				F1D136592DD9EA4E005B1D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F1D136552DD9EA4E005B1D4C /* Build configuration list for PBXNativeTarget "magicBox" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F1D136562DD9EA4E005B1D4C /* Debug */,
				F1D136572DD9EA4E005B1D4C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B60BF0742E1FB6FA00DF718C /* XCRemoteSwiftPackageReference "FloatingPanel" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/scenee/FloatingPanel";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.0.0;
			};
		};
		B61632EA2E1D22450058DB2A /* XCRemoteSwiftPackageReference "SnapKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SnapKit/SnapKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.7.1;
			};
		};
		B641B1652E24DD7D0071AF7E /* XCRemoteSwiftPackageReference "YPImagePicker" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Yummypets/YPImagePicker";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.3.0;
			};
		};
		B6A44A632E1BD26700E8C310 /* XCRemoteSwiftPackageReference "ZipArchive" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ZipArchive/ZipArchive.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.6.0;
			};
		};
		B6A44A662E1BD2EE00E8C310 /* XCRemoteSwiftPackageReference "SQLite.swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stephencelis/SQLite.swift.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.15.4;
			};
		};
		B6A44A692E1BE78E00E8C310 /* XCRemoteSwiftPackageReference "Tiercel" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Danie1s/Tiercel";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.2.6;
			};
		};
		B6A44A6C2E1BE7F400E8C310 /* XCRemoteSwiftPackageReference "SKPhotoBrowser" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/suzuki-0000/SKPhotoBrowser";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 7.1.0;
			};
		};
		B6A44A6F2E1BE88000E8C310 /* XCRemoteSwiftPackageReference "StoreHelper" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/russell-archer/StoreHelper";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.6.10;
			};
		};
		B6A44A722E1BE89B00E8C310 /* XCRemoteSwiftPackageReference "Files" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/JohnSundell/Files";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.3.0;
			};
		};
		B6A44A752E1BE8D000E8C310 /* XCRemoteSwiftPackageReference "Device" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Ekhoo/Device";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.7.0;
			};
		};
		B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/sparrowcode/PermissionsKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.1.0;
			};
		};
		B6A44A812E1BEA1F00E8C310 /* XCRemoteSwiftPackageReference "SFSafeSymbols" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SFSafeSymbols/SFSafeSymbols";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.2.0;
			};
		};
		B6A44A842E1BECD000E8C310 /* XCRemoteSwiftPackageReference "HXPhotoPicker" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SilenceLove/HXPhotoPicker";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.3;
			};
		};
		B6A44A872E1BECF600E8C310 /* XCRemoteSwiftPackageReference "Popovers" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/aheze/Popovers";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.3.2;
			};
		};
		B6A44A8A2E1BF14800E8C310 /* XCRemoteSwiftPackageReference "HorizonCalendar" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/HorizonCalendar";
			requirement = {
				branch = master;
				kind = branch;
			};
		};
		B6A44A8D2E1BF3B000E8C310 /* XCRemoteSwiftPackageReference "CountryPicker" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SURYAKANTSHARMA/CountryPicker";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.2;
			};
		};
		B6AAB6022E210D6900540D38 /* XCRemoteSwiftPackageReference "Mantis" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/guoyingtao/Mantis";
			requirement = {
				kind = exactVersion;
				version = 2.26.0;
			};
		};
		F14D31FC2DEBEB1900F80C56 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/hackiftekhar/IQKeyboardManager.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.1;
			};
		};
		F14D31FF2DEBEBE200F80C56 /* XCRemoteSwiftPackageReference "LookinServer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/QMUI/LookinServer/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.8;
			};
		};
		F1D1365A2DD9EC46005B1D4C /* XCRemoteSwiftPackageReference "opencv-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/yeatse/opencv-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.11.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B60BF0752E1FB6FA00DF718C /* FloatingPanel */ = {
			isa = XCSwiftPackageProductDependency;
			package = B60BF0742E1FB6FA00DF718C /* XCRemoteSwiftPackageReference "FloatingPanel" */;
			productName = FloatingPanel;
		};
		B61632EB2E1D22450058DB2A /* SnapKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = B61632EA2E1D22450058DB2A /* XCRemoteSwiftPackageReference "SnapKit" */;
			productName = SnapKit;
		};
		B641B1662E24DD7D0071AF7E /* YPImagePicker */ = {
			isa = XCSwiftPackageProductDependency;
			package = B641B1652E24DD7D0071AF7E /* XCRemoteSwiftPackageReference "YPImagePicker" */;
			productName = YPImagePicker;
		};
		B6A44A642E1BD26700E8C310 /* ZipArchive */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A632E1BD26700E8C310 /* XCRemoteSwiftPackageReference "ZipArchive" */;
			productName = ZipArchive;
		};
		B6A44A672E1BD2EE00E8C310 /* SQLite */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A662E1BD2EE00E8C310 /* XCRemoteSwiftPackageReference "SQLite.swift" */;
			productName = SQLite;
		};
		B6A44A6A2E1BE78E00E8C310 /* Tiercel */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A692E1BE78E00E8C310 /* XCRemoteSwiftPackageReference "Tiercel" */;
			productName = Tiercel;
		};
		B6A44A6D2E1BE7F400E8C310 /* SKPhotoBrowser */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A6C2E1BE7F400E8C310 /* XCRemoteSwiftPackageReference "SKPhotoBrowser" */;
			productName = SKPhotoBrowser;
		};
		B6A44A702E1BE88000E8C310 /* StoreHelper */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A6F2E1BE88000E8C310 /* XCRemoteSwiftPackageReference "StoreHelper" */;
			productName = StoreHelper;
		};
		B6A44A732E1BE89B00E8C310 /* Files */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A722E1BE89B00E8C310 /* XCRemoteSwiftPackageReference "Files" */;
			productName = Files;
		};
		B6A44A762E1BE8D000E8C310 /* Device */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A752E1BE8D000E8C310 /* XCRemoteSwiftPackageReference "Device" */;
			productName = Device;
		};
		B6A44A792E1BE9E800E8C310 /* CameraPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = CameraPermission;
		};
		B6A44A7B2E1BE9E800E8C310 /* FaceIDPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = FaceIDPermission;
		};
		B6A44A7D2E1BE9E800E8C310 /* NotificationPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = NotificationPermission;
		};
		B6A44A7F2E1BE9E800E8C310 /* PhotoLibraryPermission */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A782E1BE9E800E8C310 /* XCRemoteSwiftPackageReference "PermissionsKit" */;
			productName = PhotoLibraryPermission;
		};
		B6A44A822E1BEA1F00E8C310 /* SFSafeSymbols */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A812E1BEA1F00E8C310 /* XCRemoteSwiftPackageReference "SFSafeSymbols" */;
			productName = SFSafeSymbols;
		};
		B6A44A852E1BECD000E8C310 /* HXPhotoPicker */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A842E1BECD000E8C310 /* XCRemoteSwiftPackageReference "HXPhotoPicker" */;
			productName = HXPhotoPicker;
		};
		B6A44A882E1BECF600E8C310 /* Popovers */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A872E1BECF600E8C310 /* XCRemoteSwiftPackageReference "Popovers" */;
			productName = Popovers;
		};
		B6A44A8B2E1BF14800E8C310 /* HorizonCalendar */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A8A2E1BF14800E8C310 /* XCRemoteSwiftPackageReference "HorizonCalendar" */;
			productName = HorizonCalendar;
		};
		B6A44A8E2E1BF3B000E8C310 /* CountryPicker */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6A44A8D2E1BF3B000E8C310 /* XCRemoteSwiftPackageReference "CountryPicker" */;
			productName = CountryPicker;
		};
		B6AAB6032E210D6900540D38 /* Mantis */ = {
			isa = XCSwiftPackageProductDependency;
			package = B6AAB6022E210D6900540D38 /* XCRemoteSwiftPackageReference "Mantis" */;
			productName = Mantis;
		};
		F14D31FD2DEBEB1900F80C56 /* IQKeyboardManagerSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = F14D31FC2DEBEB1900F80C56 /* XCRemoteSwiftPackageReference "IQKeyboardManager" */;
			productName = IQKeyboardManagerSwift;
		};
		F14D32002DEBEBE200F80C56 /* LookinServer */ = {
			isa = XCSwiftPackageProductDependency;
			package = F14D31FF2DEBEBE200F80C56 /* XCRemoteSwiftPackageReference "LookinServer" */;
			productName = LookinServer;
		};
		F1D1365B2DD9EC46005B1D4C /* OpenCV */ = {
			isa = XCSwiftPackageProductDependency;
			package = F1D1365A2DD9EC46005B1D4C /* XCRemoteSwiftPackageReference "opencv-spm" */;
			productName = OpenCV;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = F1D1363A2DD9EA4C005B1D4C /* Project object */;
}
